{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import tensorflow as tf\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tensorflow tensorflow-gpu pandas matplotlib sklearn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install tensorflow tensorflow-gpu pandas matplotlib sklearn"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["df = pd.read_csv(os.path.join('jigsaw-toxic-comment-classification-challenge','train.csv', 'train.csv'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. Preprocess"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["!pip list"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["from tensorflow.keras.layers import TextVectorization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["X = df['comment_text']\n", "y = df[df.columns[2:]].values"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["MAX_FEATURES = 200000 # number of words in the vocab"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["vectorizer = TextVectorization(max_tokens=MAX_FEATURES,\n", "                               output_sequence_length=1800,\n", "                               output_mode='int')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["vectorizer.adapt(X.values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["vectorized_text = vectorizer(X.values)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["#MCSHBAP - map, chache, shuffle, batch, prefetch  from_tensor_slices, list_file\n", "dataset = tf.data.Dataset.from_tensor_slices((vectorized_text, y))\n", "dataset = dataset.cache()\n", "dataset = dataset.shuffle(160000)\n", "dataset = dataset.batch(16)\n", "dataset = dataset.prefetch(8) # helps bottlenecks"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["train = dataset.take(int(len(dataset)*.7))\n", "val = dataset.skip(int(len(dataset)*.7)).take(int(len(dataset)*.2))\n", "test = dataset.skip(int(len(dataset)*.9)).take(int(len(dataset)*.1))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Create Sequential Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import LSTM, Dropout, Bidirectional, Dense, Embedding"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["model = Sequential()\n", "# Create the embedding layer \n", "model.add(Embedding(MAX_FEATURES+1, 32))\n", "# Bidirectional LSTM Layer\n", "model.add(Bidirectional(LSTM(32, activation='tanh')))\n", "# Feature extractor Fully connected layers\n", "model.add(<PERSON><PERSON>(128, activation='relu'))\n", "model.add(<PERSON><PERSON>(256, activation='relu'))\n", "model.add(<PERSON><PERSON>(128, activation='relu'))\n", "# Final layer \n", "model.add(<PERSON><PERSON>(6, activation='sigmoid'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["model.compile(loss='BinaryCrossentropy', optimizer='Adam')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["model.summary()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["history = model.fit(train, epochs=1, validation_data=val)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["from matplotlib import pyplot as plt"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["plt.figure(figsize=(8,5))\n", "pd.<PERSON><PERSON><PERSON><PERSON>(history.history).plot()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Make Predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["input_text = vectorizer('You freaking suck! I am going to hit you.')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["res = model.predict(input_text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [], "source": ["(res > 0.5).astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["batch_X, batch_y = test.as_numpy_iterator().next()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["(model.predict(batch_X) > 0.5).astype(int)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["res.shape"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["from tensorflow.keras.metrics import Precision, Recall, CategoricalAccuracy"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["pre = Precision()\n", "re = Recall()\n", "acc = CategoricalAccuracy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["for batch in test.as_numpy_iterator(): \n", "    # Unpack the batch \n", "    X_true, y_true = batch\n", "    # Make a prediction \n", "    yhat = model.predict(X_true)\n", "    \n", "    # Flatten the predictions\n", "    y_true = y_true.flatten()\n", "    yhat = yhat.flatten()\n", "    \n", "    pre.update_state(y_true, yhat)\n", "    re.update_state(y_true, yhat)\n", "    acc.update_state(y_true, yhat)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["print(f'Precision: {pre.result().numpy()}, Recall:{re.result().numpy()}, Accuracy:{acc.result().numpy()}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 5. Test and Gradio"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["!pip install gradio jinja2"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}, "tags": []}, "outputs": [], "source": ["import tensorflow as tf\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["model.save('toxicity.h5')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["model = tf.keras.models.load_model('toxicity.h5')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["input_str = vectorizer('hey i freaken hate you!')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["res = model.predict(np.expand_dims(input_str,0))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["res"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["def score_comment(comment):\n", "    vectorized_comment = vectorizer([comment])\n", "    results = model.predict(vectorized_comment)\n", "    \n", "    text = ''\n", "    for idx, col in enumerate(df.columns[2:]):\n", "        text += '{}: {}\\n'.format(col, results[0][idx]>0.5)\n", "    \n", "    return text"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["interface = gr.Interface(fn=score_comment, \n", "                         inputs=gr.inputs.Textbox(lines=2, placeholder='Comment to score'),\n", "                        outputs='text')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["interface.launch(share=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}
# Comment Toxicity Classification Project Report

## Project Overview

This project implements a deep learning model for multi-label classification of toxic comments using TensorFlow/Keras. The model is designed to identify various types of toxicity in text comments, making it useful for content moderation and online safety applications.

## Dataset

### Source
- **Dataset**: Jigsaw Toxic Comment Classification Challenge dataset
- **Source**: Kaggle competition dataset
- **Files Used**: 
  - `train.csv` - Training data with labeled comments
  - `test.csv` - Test data for evaluation
  - `sample_submission.csv` - Sample submission format

### Dataset Structure
The dataset contains the following columns:
- `id` - Unique identifier for each comment
- `comment_text` - The actual text content of the comment
- **Target Labels** (6 binary classification targets):
  - `toxic` - General toxicity
  - `severe_toxic` - Severely toxic content
  - `obscene` - Obscene language
  - `threat` - Threatening language
  - `insult` - Insulting content
  - `identity_hate` - Identity-based hate speech

### Data Characteristics
- **Problem Type**: Multi-label binary classification
- **Input**: Text comments of varying lengths
- **Output**: 6 binary labels (0 or 1 for each toxicity category)
- **Data Split**: 70% training, 20% validation, 10% testing

## Model Architecture

### Model Type
**Bidirectional LSTM with Dense Layers** - A sequential deep learning model optimized for text classification

### Architecture Details

1. **Text Preprocessing Layer**
   - `TextVectorization` layer with:
     - Maximum vocabulary size: 200,000 tokens
     - Maximum sequence length: 1,800 tokens
     - Output mode: Integer encoding

2. **Embedding Layer**
   - Input dimension: 200,001 (vocabulary size + 1)
   - Output dimension: 32
   - Purpose: Convert integer tokens to dense vector representations

3. **Bidirectional LSTM Layer**
   - Units: 32
   - Activation: tanh
   - Bidirectional processing for better context understanding

4. **Dense Feature Extraction Layers**
   - Dense layer 1: 128 units, ReLU activation
   - Dense layer 2: 256 units, ReLU activation  
   - Dense layer 3: 128 units, ReLU activation

5. **Output Layer**
   - 6 units (one for each toxicity category)
   - Sigmoid activation for multi-label classification

### Model Configuration
- **Loss Function**: Binary Crossentropy (suitable for multi-label classification)
- **Optimizer**: Adam
- **Batch Size**: 16
- **Training Epochs**: 1 (as shown in the notebook)

## Data Processing Pipeline

### Text Vectorization
- Vocabulary limited to top 200,000 most frequent words
- Sequences padded/truncated to 1,800 tokens
- Integer encoding for efficient processing

### Data Pipeline Optimization
The project implements TensorFlow's recommended data pipeline optimizations:
- **MCSHBAP Pattern**: Map, Cache, Shuffle, Batch, Prefetch
- **Caching**: Dataset cached in memory for faster access
- **Shuffling**: 160,000 samples shuffled for better training
- **Batching**: 16 samples per batch
- **Prefetching**: 8 batches prefetched to reduce I/O bottlenecks

## Model Evaluation

### Metrics Used
- **Precision**: Measures the accuracy of positive predictions
- **Recall**: Measures the model's ability to find all positive instances
- **Categorical Accuracy**: Overall accuracy across all categories

### Evaluation Process
- Model evaluated on held-out test set (10% of data)
- Predictions thresholded at 0.5 for binary classification
- Metrics calculated across all toxicity categories

## Implementation Features

### Model Persistence
- Model saved as `toxicity.h5` for future use
- Can be loaded and used for inference without retraining

### Interactive Interface
- **Gradio Integration**: Web-based interface for real-time toxicity scoring
- **User Input**: Text box for entering comments to analyze
- **Output**: Boolean predictions for each toxicity category
- **Deployment**: Shareable interface via Gradio's share functionality

### Prediction Function
```python
def score_comment(comment):
    vectorized_comment = vectorizer([comment])
    results = model.predict(vectorized_comment)
    
    text = ''
    for idx, col in enumerate(df.columns[2:]):
        text += '{}: {}\n'.format(col, results[0][idx]>0.5)
    
    return text
```

## Technical Stack

### Core Libraries
- **TensorFlow/Keras**: Deep learning framework
- **Pandas**: Data manipulation and analysis
- **NumPy**: Numerical computing
- **Matplotlib**: Data visualization

### Additional Tools
- **Gradio**: Interactive web interface creation
- **Jinja2**: Template engine (dependency for Gradio)

## Model Strengths

1. **Multi-label Classification**: Can detect multiple types of toxicity simultaneously
2. **Bidirectional Processing**: LSTM processes text in both directions for better context
3. **Scalable Architecture**: Can handle large vocabularies (200K tokens)
4. **Production Ready**: Includes model saving and interactive interface
5. **Optimized Pipeline**: Efficient data processing with TensorFlow optimizations

## Potential Improvements

1. **Training Duration**: Only 1 epoch used - more training could improve performance
2. **Hyperparameter Tuning**: Architecture and learning parameters could be optimized
3. **Advanced Architectures**: Could explore Transformer-based models (BERT, etc.)
4. **Data Augmentation**: Text augmentation techniques could improve robustness
5. **Class Imbalance**: Address potential imbalance in toxicity categories

## Use Cases

- **Content Moderation**: Automated screening of user-generated content
- **Social Media Platforms**: Real-time comment filtering
- **Online Forums**: Community safety and moderation
- **Educational Platforms**: Maintaining safe learning environments
- **Customer Service**: Monitoring and flagging inappropriate communications

## Conclusion

This project successfully implements a multi-label text classification system for toxicity detection using modern deep learning techniques. The combination of bidirectional LSTM architecture, efficient data processing, and user-friendly interface makes it a practical solution for real-world content moderation applications. While the current implementation shows promise, there are opportunities for enhancement through extended training, architectural improvements, and advanced NLP techniques.
